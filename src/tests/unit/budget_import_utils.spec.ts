import { describe, it, expect } from 'vitest';
import {
	matchColumns,
	parseWbsCode,
	buildCategoryPrefix,
	classifyRow,
	applyCategoriesToRows,
	validateColumnMapping,
	transformToImportData,
	type ColumnMapping,
	type ProcessedRow,
} from '$lib/budget_import_utils';

describe('matchColumns', () => {
	it('should match common column headers', () => {
		const headers = ['Code', 'Description', 'Quantity', 'UOM', 'Rate', 'SubTotal'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
		expect(mapping.subtotal).toBe(5);
	});

	it('should handle case-insensitive matching', () => {
		const headers = ['code', 'DESCRIPTION', 'qty', 'Unit', 'material rate'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
	});

	it('should handle WBS code variations', () => {
		const headers = ['WBS Code', 'WBS.Code', 'Cost Code'];
		const mapping1 = matchColumns([headers[0]]);
		const mapping2 = matchColumns([headers[1]]);
		const mapping3 = matchColumns([headers[2]]);

		expect(mapping1.code).toBe(0);
		expect(mapping2.code).toBe(0);
		expect(mapping3.code).toBe(0);
	});

	it('should not map unknown headers', () => {
		const headers = ['Unknown1', 'Unknown2', 'Random'];
		const mapping = matchColumns(headers);

		expect(Object.values(mapping).every((v) => v === undefined)).toBe(true);
	});
});

describe('parseWbsCode', () => {
	it('should parse single level codes', () => {
		const result = parseWbsCode('1');
		expect(result).toEqual({
			level: 1,
			in_level_code: '1',
			parent_code: null,
		});
	});

	it('should parse multi-level codes', () => {
		const result = parseWbsCode('1.2.3');
		expect(result).toEqual({
			level: 3,
			in_level_code: '3',
			parent_code: '1.2',
		});
	});

	it('should handle complex codes', () => {
		const result = parseWbsCode('A.B.C.D.E');
		expect(result).toEqual({
			level: 5,
			in_level_code: 'E',
			parent_code: 'A.B.C.D',
		});
	});

	it('should throw error for invalid codes', () => {
		expect(() => parseWbsCode('')).toThrow('Invalid WBS code');
		expect(() => parseWbsCode(null as any)).toThrow('Invalid WBS code');
	});
});

describe('buildCategoryPrefix', () => {
	it('should build prefix from category stack', () => {
		const stack = ['Site Work', 'Excavation'];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});

	it('should handle empty stack', () => {
		const prefix = buildCategoryPrefix([]);
		expect(prefix).toBe('');
	});

	it('should trim category names', () => {
		const stack = [' Site Work ', ' Excavation '];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});
});

describe('classifyRow', () => {
	it('should classify detail rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			code: '1.1.1',
			description: 'Concrete work',
			quantity: 100,
		};
		expect(classifyRow(row)).toBe('detail');
	});

	it('should classify category rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'Site Work',
		};
		expect(classifyRow(row)).toBe('category');
	});

	it('should classify summary rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'TOTAL Site Work',
		};
		expect(classifyRow(row)).toBe('summary');
	});

	it('should classify ignore rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
		};
		expect(classifyRow(row)).toBe('ignore');
	});
});

describe('applyCategoriesToRows', () => {
	it('should apply category prefixes to detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 2, description: 'Concrete Work' },
			{ originalIndex: 3, code: '2.1', description: 'Foundation', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');

		expect(result[2].classification).toBe('category');
		expect(result[3].classification).toBe('detail');
		expect(result[3].categoryPrefix).toBe('[Site Work][Concrete Work]');
		expect(result[3].finalDescription).toBe('[Site Work][Concrete Work]Foundation');
	});

	it('should handle category edits', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
		];

		const categoryEdits = { 0: 'Modified Site Work' };
		const result = applyCategoriesToRows(rows, categoryEdits);

		expect(result[1].categoryPrefix).toBe('[Modified Site Work]');
		expect(result[1].finalDescription).toBe('[Modified Site Work]Excavation');
	});
});

describe('validateColumnMapping', () => {
	it('should validate required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			quantity: 2,
			rate: 3,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(true);
		expect(result.errors).toEqual([]);
	});

	it('should detect missing required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			// missing quantity and rate
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('quantity column is required');
		expect(result.errors).toContain('rate column is required');
	});

	it('should detect duplicate mappings', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 0, // duplicate index
			quantity: 1,
			rate: 2,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('Column index 0 is mapped to multiple fields');
	});
});

describe('transformToImportData', () => {
	it('should transform classified rows to import format', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'category' as const,
				description: 'Site Work',
				finalDescription: 'Site Work',
			},
			{
				originalIndex: 1,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Excavation',
				finalDescription: '[Site Work]Excavation',
				quantity: 100,
				uom: 'm3',
				rate: 50,
				factor: 1.1,
			},
			{
				originalIndex: 2,
				classification: 'ignore' as const,
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.project_id).toBe('project-123');
		expect(result.items).toHaveLength(1);
		expect(result.items[0]).toEqual({
			code: '1.1',
			description: '[Site Work]Excavation',
			quantity: 100,
			unit: 'm3',
			material_rate: 50,
			factor: 1.1,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});

	it('should handle missing values gracefully', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Test item',
				finalDescription: 'Test item',
				// missing quantity, rate, etc.
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.items[0]).toEqual({
			code: '1.1',
			description: 'Test item',
			quantity: 0,
			unit: '',
			material_rate: 0,
			factor: undefined,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});
});
