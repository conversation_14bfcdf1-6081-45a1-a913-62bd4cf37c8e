import * as XLSX from 'xlsx';
import type { ProcessedRow, ExcelRow } from './budget_import_utils';

export interface ExcelParseResult {
	rows: ProcessedRow[];
	hasMultipleSheets: boolean;
	hasYellowFill: boolean;
	headers: string[];
	headerRowIndex: number;
}

/**
 * Parse Excel file and extract budget data
 */
export function parseExcelFile(buffer: ArrayBuffer): ExcelParseResult {
	// Read workbook with cell styles for yellow fill detection
	const workbook = XLSX.read(buffer, {
		type: 'buffer',
		cellStyles: true,
		cellDates: true,
	});

	const hasMultipleSheets = workbook.SheetNames.length > 1;

	// Use the first worksheet
	const firstSheetName = workbook.SheetNames[0];
	const worksheet = workbook.Sheets[firstSheetName];

	// Convert to array of arrays to preserve cell information
	const rawData: any[][] = XLSX.utils.sheet_to_json(worksheet, {
		header: 1,
		defval: null,
		raw: false, // Get formatted values
	});

	// Find header row (row with most non-empty cells in first 10 rows)
	const headerRowIndex = findHeaderRow(rawData);
	const headers = rawData[headerRowIndex] || [];

	// Process data rows (everything after header)
	const dataRows = rawData.slice(headerRowIndex + 1);

	// Check for yellow fill in any cells
	let hasYellowFill = false;
	const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

	for (let row = range.s.r; row <= range.e.r; row++) {
		for (let col = range.s.c; col <= range.e.c; col++) {
			const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
			const cell = worksheet[cellAddress];

			if (cell && cell.s && cell.s.fill && cell.s.fill.fgColor) {
				const fillColor = cell.s.fill.fgColor.rgb;
				// Check for yellow fill (FFFFFF00 or similar yellow colors)
				if (fillColor === 'FFFFFF00' || fillColor === 'FFFF00') {
					hasYellowFill = true;
					break;
				}
			}
		}
		if (hasYellowFill) break;
	}

	// Convert data rows to ProcessedRow format
	const rows: ProcessedRow[] = dataRows.map((row, index) => {
		const processedRow: ProcessedRow = {
			originalIndex: headerRowIndex + 1 + index,
			hasYellowFill: false, // We'll check this per row if needed
		};

		// Map each column to the processed row
		headers.forEach((header, colIndex) => {
			const value = row[colIndex];
			if (value === null || value === undefined || value === '') return;

			const headerStr = String(header).toLowerCase().trim();

			// Try to map common column names
			if (headerStr.includes('code') || headerStr.includes('wbs')) {
				processedRow.code = String(value).trim();
			} else if (
				headerStr.includes('description') ||
				headerStr.includes('scope') ||
				headerStr.includes('item')
			) {
				processedRow.description = String(value).trim();
			} else if (headerStr.includes('quantity') || headerStr.includes('qty')) {
				processedRow.quantity = parseFloat(String(value)) || 0;
			} else if (headerStr.includes('unit') && !headerStr.includes('rate')) {
				processedRow.uom = String(value).trim();
			} else if (headerStr.includes('rate')) {
				processedRow.rate = parseFloat(String(value)) || 0;
			} else if (headerStr.includes('subtotal') || headerStr.includes('sub total')) {
				processedRow.subtotal = parseFloat(String(value)) || 0;
			} else if (headerStr.includes('factor') || headerStr.includes('multiplier')) {
				processedRow.factor = parseFloat(String(value)) || 0;
			}
		});

		return processedRow;
	});

	return {
		rows,
		hasMultipleSheets,
		hasYellowFill,
		headers: headers.map((h) => String(h || '')),
		headerRowIndex,
	};
}

/**
 * Find the header row by looking for the row with the most non-empty cells
 * in the first 10 rows
 */
function findHeaderRow(data: any[][]): number {
	let maxNonEmptyCells = 0;
	let headerRowIndex = 0;

	// Check first 10 rows
	const rowsToCheck = Math.min(10, data.length);

	for (let i = 0; i < rowsToCheck; i++) {
		const row = data[i] || [];
		const nonEmptyCells = row.filter(
			(cell) => cell !== null && cell !== undefined && String(cell).trim() !== '',
		).length;

		if (nonEmptyCells > maxNonEmptyCells) {
			maxNonEmptyCells = nonEmptyCells;
			headerRowIndex = i;
		}
	}

	return headerRowIndex;
}

/**
 * Get raw Excel data as array of objects for preview
 */
export function getExcelPreviewData(buffer: ArrayBuffer, maxRows: number = 20): ExcelRow[] {
	const workbook = XLSX.read(buffer, { type: 'buffer' });
	const firstSheetName = workbook.SheetNames[0];
	const worksheet = workbook.Sheets[firstSheetName];

	const data: ExcelRow[] = XLSX.utils.sheet_to_json(worksheet, {
		defval: null,
		raw: false,
	});

	return data.slice(0, maxRows);
}

/**
 * Check if a cell has yellow fill
 */
function hasYellowFill(cell: XLSX.CellObject): boolean {
	if (!cell || !cell.s || !cell.s.fill || !cell.s.fill.fgColor) {
		return false;
	}

	const fillColor = cell.s.fill.fgColor.rgb;
	return fillColor === 'FFFFFF00' || fillColor === 'FFFF00';
}

/**
 * Validate Excel file format
 */
export function validateExcelFile(file: File): { isValid: boolean; errors: string[] } {
	const errors: string[] = [];

	// Check file extension
	if (!file.name.toLowerCase().endsWith('.xlsx')) {
		errors.push('File must be an Excel (.xlsx) file');
	}

	// Check file size (10MB limit)
	const maxSize = 10 * 1024 * 1024; // 10MB
	if (file.size > maxSize) {
		errors.push('File size must be less than 10MB');
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}
